body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  background: #fff;
}

@font-face {
  font-display: swap;
  font-family: 'HostGrotesk';
  font-weight: 400;
  src: url('../public/fonts/host-grotesk-v4-latin-regular.woff2') format('woff2');
}

@font-face {
  font-display: swap;
  font-family: 'HostGroteskBold';
  font-weight: 500;
  src: url('../public/fonts/host-grotesk-v4-latin-500.woff2') format('woff2');
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'HostGrotesk', 'Roboto', 'Helvetica', sans-serif;
}

.markdown-container h1,
.markdown-container h2,
.markdown-container h3,
.markdown-container h4,
.markdown-container h5,
.markdown-container h6 {
  font-family: 'HostGrotesk';
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.4);
}

.conversation-wrapper-inner,
.conversation-wrapper,
#sidenav-infinite-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

#source-list-scroll::-webkit-scrollbar-track,
#data-visualization-tab-scroll::-webkit-scrollbar-track {
  background: transparent;
}

#source-list-scroll::-webkit-scrollbar-thumb,
#data-visualization-tab-scroll::-webkit-scrollbar-thumb {
  background-color: #ABCCFC;
  border-radius: 8px;
}

#source-list-scroll::-webkit-scrollbar-thumb:hover,
#data-visualization-tab-scroll::-webkit-scrollbar-thumb:hover {
  background-color: #ABCCFC;
  border-radius: 8px;
}

#source-list-scroll,
#data-visualization-tab-scroll {
  scrollbar-width: thin;
  scrollbar-color: #ABCCFC transparent;
  background: rgb(245, 249, 254);
  box-sizing: 'content-box';
}

#data-visualization-mean-section-scroll {
  overflow-y: scroll;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  background: rgb(245, 249, 254);
}

#data-visualization-mean-section-scroll::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

#data-visualization-mean-section-scroll::-webkit-scrollbar-track {
  background: transparent;
}

#data-visualization-mean-section-scroll::-webkit-scrollbar-thumb {
  background: transparent;
}