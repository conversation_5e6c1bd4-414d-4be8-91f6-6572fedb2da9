import { useEffect, useState, useRef, useContext } from "react";
import NewChatButtonController from "../../controllers/NewChatButtonController";
import {
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Box,
  ListSubheader,
  Divider,
} from "@mui/material";
import { useNavigate } from 'react-router-dom';
import { get } from "../../services/apiService";
import { useTheme } from '@mui/material/styles';
import Logo from "../Common/Logo/Logo";
import { NO_CHATS_HISTORY_FOUND_LABEL } from "../../utils/labels";
import { motion, AnimatePresence } from "framer-motion";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import HelpOutlineOutlinedIcon from "@mui/icons-material/HelpOutlineOutlined";
import BugReportOutlinedIcon from '@mui/icons-material/BugReportOutlined';
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import GitHubIcon from "@mui/icons-material/GitHub";
import CustomTooltip from '../Common/CustomTooltip';
import { LayoutContext } from "../Layout/LayoutContext";
import ViewSidebarOutlinedIcon from '@mui/icons-material/ViewSidebarOutlined';

type Conversation = {
  id: string;
  title: string;
  created_at: string;
};

type ConversationsResp = {
  success: boolean;
  data: {
    conversations: Conversation[];
  };
};

interface SidebarProps {
  isMobileOrTablet: boolean;
  handleMobileDrawerClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isMobileOrTablet, handleMobileDrawerClose }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loadingConversations, setLoadingConversations] = useState(true);
  const [errorConversations, setErrorConversations] = useState<string | null>(null);
  const [showAll, setShowAll] = useState(false);
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
  const chatItemRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const [isHovered, setIsHovered] = useState(false);

  // Calculate header and footer heights dynamically
  const headerFooterHeight = 200;

  const commonScrollStyles = {
    overflowY: "auto",
    position: "relative",
    scrollbarWidth: "thin",
    scrollbarColor: theme.palette.background.paper,
    px: 1,
    pt: 2,
  };

  const {
    updateErrorMessageState,
    updateConversationId,
    refreshChatHistory,
    updateRefreshChatHistory,
    updateCenteredContentState,
    conversationId: contextConversationId,
    updateAppLoaderState,
    updateIsStreamingContent,
    updateStreamingErrorState,
    isSidebarCollapsed,
    toggleSidebarCollapsed,
    userExpandedSidebar
  } = useContext(LayoutContext);

  const areConversationsEqual = (arr1: Conversation[], arr2: Conversation[]): boolean => {
    if (arr1.length !== arr2.length) {
      return false;
    }
    for (let i = 0; i < arr1.length; i++) {
      if (arr1[i].id !== arr2[i].id || arr1[i].title !== arr2[i].title || arr1[i].created_at !== arr2[i].created_at) {
        return false;
      }
    }
    return true;
  };

  const fetchData = async (): Promise<Conversation[]> => {
    setErrorConversations(null);
    setLoadingConversations(true);
    try {
      const response: ConversationsResp = await get<ConversationsResp>('/conversations');
      if (response.success) {
        const newConversations = response.data.conversations;
        if (!areConversationsEqual(conversations, newConversations)) {
          setConversations(newConversations);
        }
        return newConversations;
      }
      return [];
    } catch (error) {
      setErrorConversations("Failed to fetch chat history");
      updateErrorMessageState("Failed to fetch chat history");
      return [];
    } finally {
      setLoadingConversations(false);
    }
  };

  useEffect(() => {
    const handleChatHistoryLogic = async () => {
      updateErrorMessageState('');
      if (contextConversationId || refreshChatHistory) {
        const shouldFetchData = refreshChatHistory || !conversations.some(c => c.id === contextConversationId);

        let conversationsData;
        if (shouldFetchData) {
          conversationsData = await fetchData();
        } else {
          conversationsData = conversations;
          setLoadingConversations(false);
        }

        const matchingChat = conversationsData.find((conversation) => conversation.id === contextConversationId);
        if (matchingChat) {
          setSelectedConversationId(matchingChat.id);
          if (conversationsData.indexOf(matchingChat) >= 5) {
            setShowAll(true);
          }
        } else {
          setSelectedConversationId(null);
        }
      } else {
        setSelectedConversationId(null);
      }

      if (refreshChatHistory) {
        updateRefreshChatHistory('');
      }
    };
    handleChatHistoryLogic();
  }, [contextConversationId, refreshChatHistory, conversations]);

  useEffect(() => {
    if (selectedConversationId && chatItemRefs.current[selectedConversationId]) {
      const timeoutId = setTimeout(() => {
        chatItemRefs.current[selectedConversationId]!.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [showAll, selectedConversationId]);

  const handleInteractionClick = () => {
    if (isMobileOrTablet) {
      handleMobileDrawerClose();
    } else if (!isSidebarCollapsed && !userExpandedSidebar) {
      toggleSidebarCollapsed(false);
    }
  };

  const handleOnClickHistoryItem = (clickedConversationId: string) => {
    handleInteractionClick();
    updateIsStreamingContent(false);
    updateStreamingErrorState(false);
    updateAppLoaderState(true);
    updateCenteredContentState(false);
    updateErrorMessageState(null);
    updateRefreshChatHistory(true);
    updateConversationId(clickedConversationId);
    setSelectedConversationId(clickedConversationId);
    sessionStorage.removeItem('isNewConversationPending');
    sessionStorage.removeItem('pendingNewConversationMessage');
    navigate("/" + clickedConversationId);
  };

  const handleNewChatClick = () => {
    handleInteractionClick();
    sessionStorage.removeItem('isNewConversationPending');
    sessionStorage.removeItem('pendingNewConversationMessage');
    updateStreamingErrorState(false);
    updateCenteredContentState(false);
    updateErrorMessageState(null);
    updateConversationId('');
    setSelectedConversationId(null);
    navigate("/");
  };

  const toggleShowAll = () => {
    setShowAll(prev => !prev);
  };

  const recordsToShow = showAll ? conversations : conversations.slice(0, 5);
  const showMoreButtonVisible = conversations.length > 5;

  const bottomLinks = [
    { icon: InfoOutlinedIcon, label: "About" },
    { icon: HelpOutlineOutlinedIcon, label: "Help" },
    { icon: GitHubIcon, label: "Github" },
    {
      icon: BugReportOutlinedIcon, label: "Report a bug", onClick: () => {
        handleInteractionClick();
        window.open('https://forms.gle/PjFVVsum8J3MvYVp9', '_blank')
      }
    },
  ];

  return (
    <Box
      display="flex"
      flexDirection="column"
      height="100%"
      width="100%"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      sx={{
        backgroundColor: (isSidebarCollapsed && isHovered && !isMobileOrTablet) ? theme.elevation.paperElevationTwo : theme.elevation.paperElevationZero,
      }}
    >
      <Box
        className="sidenav-container"
        position="relative"
        height="100%"
        display="flex"
        flexDirection="column"
        onClick={(event: React.MouseEvent<HTMLDivElement>) => {
          const target = event.target as HTMLElement;
          const isButtonOrLink = target.closest('button, a, .MuiListItemButton-root, #impactai-logo, .MuiSvgIcon-root');
          if (!isMobileOrTablet && isSidebarCollapsed && !isButtonOrLink) {
            toggleSidebarCollapsed(true);
          }
        }}
        sx={{
          cursor: (!isMobileOrTablet && isSidebarCollapsed) ? 'pointer' : 'default',
        }}
      >
        <Box px={1} mt={3}>
          {isSidebarCollapsed && !isMobileOrTablet ? (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'flex-start',
                width: '100%',
              }}
            >
              <Box
                onClick={() => toggleSidebarCollapsed(true)}
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '80%',
                  cursor: 'pointer',
                  py: 1,
                  '&:hover': {
                    borderRadius: '50%',
                    backgroundColor: theme.elevation.paperElevationTwo,
                    transform: 'scale(1.05)',
                  },
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                <ViewSidebarOutlinedIcon
                  sx={{
                    fontSize: '24px',
                    color: theme.palette.text.secondary,
                  }}
                />
              </Box>
            </Box>
          ) : (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                width: '100%',
                pl: 0,
                pr: 1.5,
                maxWidth: '100%',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  minWidth: 0,
                  flex: '0 0 auto',
                  maxWidth: 'calc(100% - 56px)',
                  pl: 1.5,
                }}
              >
                <Logo
                  onClick={handleNewChatClick}
                  isCollapsed={false}
                  sx={{
                    justifyContent: 'flex-start',
                    transform: 'scale(0.7)',
                    minWidth: 0,
                    marginBottom: 0,
                    marginLeft: 0,
                    paddingLeft: 0,
                    px: 0,
                  }}
                />
              </Box>
              <Box
                sx={{
                  ml: 'auto',
                  pl: 2,
                }}
              >
                <Box
                  onClick={() => toggleSidebarCollapsed(false)}
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    cursor: 'pointer',
                    width: '32px',
                    height: '32px',
                    '&:hover': {
                      borderRadius: '50%',
                      backgroundColor: theme.elevation.paperElevationTwo,
                      transform: 'scale(1.05)',
                    },
                    transition: 'all 0.2s ease-in-out',
                  }}
                >
                  <ViewSidebarOutlinedIcon
                    sx={{
                      fontSize: '24px',
                      color: theme.palette.text.secondary,
                    }}
                  />
                </Box>
              </Box>
            </Box>
          )}
          <Box sx={{ mt: '24px', px: isSidebarCollapsed ? 0 : 1.5, display: 'flex', justifyContent: 'center' }}>
            <NewChatButtonController
              onClick={handleNewChatClick}
              isCollapsed={isSidebarCollapsed && !isMobileOrTablet}
            />
          </Box>
        </Box>

        <Box sx={{ mt: '32px', flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0 }}>
          {((!loadingConversations && !errorConversations && conversations.length === 0) || errorConversations) && (
            <Box sx={commonScrollStyles} id="sidenav-infinite-scroll">
              {!isSidebarCollapsed && (
                <ListSubheader component="div" sx={{ paddingTop: 2, paddingBottom: 1, px: 2 }}>
                  {NO_CHATS_HISTORY_FOUND_LABEL}
                </ListSubheader>
              )}
            </Box>
          )}
          <Box
            sx={{
              ...commonScrollStyles,
              visibility: (isSidebarCollapsed) ? 'hidden' : 'visible',
              flex: 1,
              overflow: (isSidebarCollapsed) ? 'hidden' : 'auto',
              maxHeight: `calc(100vh - ${headerFooterHeight}px)`,
            }}
            id="sidenav-infinite-scroll"
          >
          <>
            {conversations.length > 0 && (
              <AnimatePresence>
                {recordsToShow.map((conversation) => (
                  <motion.div
                    key={conversation.id}
                    ref={(el) => (chatItemRefs.current[conversation.id] = el)}
                    initial={{ opacity: 0.5, height: 'auto' }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{
                      duration: 0.1,
                      ease: 'linear',
                    }}
                    data-chat-id={conversation.id}
                  >
                    <CustomTooltip 
                      content={conversation.title} 
                      placement="right"
                      disableOnScroll={true}
                      scrollContainer="sidenav-infinite-scroll"
                    >
                      <ListItemButton
                        onClick={() => handleOnClickHistoryItem(conversation.id)}
                        selected={selectedConversationId === conversation.id}
                        disableGutters={true}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          px: isSidebarCollapsed ? 0.5 : 1.5,
                          py: '6px',
                          borderRadius: selectedConversationId === conversation.id ? '4px' : '0px',
                          backgroundColor: selectedConversationId === conversation.id ? theme.elevation.paperElevationSixteen : 'transparent',
                          '&.Mui-selected': {
                            backgroundColor: theme.elevation.paperElevationSixteen,
                          },
                          '&:hover': {
                            borderRadius: '4px',
                            background: theme.elevation.paperElevationTwo,
                          },
                          '&.Mui-selected:hover': {
                            background: theme.elevation.paperElevationTwo,
                          },
                          '&:active': {
                            borderRadius: '4px',
                            background: theme.elevation.paperElevationTwo,
                          },
                          justifyContent: isSidebarCollapsed ? 'center' : 'flex-start',
                        }}
                      >
                        {!isSidebarCollapsed && (
                          <ListItemText
                            primary={conversation.title}
                            sx={{
                              color: theme.palette.text.secondary,
                              maxWidth: '100%',
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              '& .MuiTypography-root': {
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                              },
                            }}
                          />
                        )}
                      </ListItemButton>
                    </CustomTooltip>
                  </motion.div>
                ))}
              </AnimatePresence>
            )}

            {showMoreButtonVisible && !isSidebarCollapsed && (
              <ListItemButton
                onClick={toggleShowAll}
                disableGutters={true}
                sx={{
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  px: 1.5,
                  py: 0,
                  width: '100%',
                  '&:hover': {
                    backgroundColor: theme.elevation.paperElevationSixteen,
                    borderRadius: '4px',
                  },
                  '&:focus': {
                    outline: 'none',
                  },
                  '&:active': {
                    outline: 'none',
                    backgroundColor: theme.elevation.paperElevationSixteen,
                  },
                  justifyContent: 'flex-start',
                  position: 'sticky',
                  bottom: 0,
                  zIndex: 1,
                  backgroundColor: theme.elevation.paperElevationTwo,
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: '35px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'transparent',
                    color: theme.palette.text.secondary,
                  }}
                >
                  {showAll ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
                </ListItemIcon>
                <ListSubheader
                  component="div"
                  disableGutters
                  sx={{
                    flex: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                    backgroundColor: 'transparent',
                    color: theme.palette.text.secondary,
                    '&:hover': { backgroundColor: 'transparent' },
                    '&:focus': { outline: 'none' },
                    '&:active': { outline: 'none' },
                  }}
                >
                  {showAll ? "Show Less" : "Show More"}
                </ListSubheader>
              </ListItemButton>
            )}
          </>
          </Box>
        </Box>

        {!isSidebarCollapsed && (
          <Divider sx={{
            my: 2,
            borderColor: theme.divider,
            mx: 2,
            width: 'auto',
          }} />
        )}

        {!isSidebarCollapsed && (
          <Box className="sidenav-action-list-wrapper" px={1} mb={2} position="sticky" zIndex={1}>
            <List>
              {bottomLinks.map((link) => (
                <ListItemButton
                  key={link.label}
                  disableGutters={true}
                  sx={{
                    px: 1.5,
                    py: 1,
                    display: 'flex',
                    alignItems: 'center',
                    cursor: link.onClick ? 'pointer' : 'default',
                    '&:hover': {
                      borderRadius: '4px',
                      backgroundColor: link.onClick ? theme.elevation.paperElevationTwo : 'transparent',
                    },
                    '&:active': {
                      borderRadius: '4px',
                      backgroundColor: link.onClick ? theme.elevation.paperElevationTwo : 'transparent',
                    },
                    justifyContent: 'flex-start',
                  }}
                  onClick={link.onClick ? () => {
                    handleInteractionClick();
                    link.onClick!();
                  } : undefined}
                >
                  <ListItemIcon
                    sx={{
                      color: theme.palette.text.secondary,
                      minWidth: '25px',
                      mr: 1,
                      '& svg': {
                        fontSize: '1.25rem',
                      },
                      justifyContent: 'center',
                    }}
                  >
                    {<link.icon fontSize="inherit" />}
                  </ListItemIcon>
                  <ListItemText
                    primary={link.label}
                    primaryTypographyProps={{
                      color: theme.palette.text.secondary,
                    }}
                  />
                </ListItemButton>
              ))}
            </List>
          </Box>
        )}
      </Box>
    </Box >
  );
};

export default Sidebar;